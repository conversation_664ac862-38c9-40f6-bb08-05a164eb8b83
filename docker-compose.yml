version: '3.8'

services:
  mongodb:
    image: mongo:7-jammy
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
      MONGO_INITDB_DATABASE: sainpse_db
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=******************************************************************
      - DATABASE_NAME=sainpse_db
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - mongodb
      - redis
    volumes:
      - .:/app
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  mongodb_data:
  redis_data:
