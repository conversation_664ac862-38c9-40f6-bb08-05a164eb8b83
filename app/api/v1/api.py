"""
Main API router for version 1.

This module combines all API endpoints and creates the main router
for API version 1.
"""

from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, membership

api_router = APIRouter()

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(membership.router, prefix="/membership", tags=["membership"])
