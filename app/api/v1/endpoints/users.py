"""
User management endpoints.

This module provides endpoints for user profile management and
user-related operations.
"""

from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status
from beanie import PydanticObjectId

from app.api.deps import get_current_user_id
from app.models.user import User
from app.schemas.user import UserResponse, UserUpdate

router = APIRouter()


@router.get("/me", response_model=UserResponse)
async def get_current_user(current_user_id: str = Depends(get_current_user_id)) -> UserResponse:
    """
    Get current user profile.

    Args:
        current_user_id: ID of the current authenticated user

    Returns:
        UserResponse: Current user information

    Raises:
        HTTPException: If user is not found
    """
    try:
        user_object_id = PydanticObjectId(current_user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        return UserResponse(
            id=str(user.id), email=user.email, full_name=user.full_name, is_active=user.is_active
        )
    except Exception:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate, current_user_id: str = Depends(get_current_user_id)
) -> UserResponse:
    """
    Update current user profile.

    Args:
        user_update: User update data
        current_user_id: ID of the current authenticated user

    Returns:
        UserResponse: Updated user information

    Raises:
        HTTPException: If user is not found or update fails
    """
    try:
        user_object_id = PydanticObjectId(current_user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="User not found")

        # Update fields if provided
        update_data = user_update.model_dump(exclude_unset=True)
        if update_data:
            update_data["updated_at"] = datetime.utcnow()
            await user.update({"$set": update_data})

            # Refresh user data
            user = await User.get(user_object_id)

        return UserResponse(
            id=str(user.id), email=user.email, full_name=user.full_name, is_active=user.is_active
        )
    except Exception as e:
        if "User not found" in str(e):
            raise e
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Failed to update user")
