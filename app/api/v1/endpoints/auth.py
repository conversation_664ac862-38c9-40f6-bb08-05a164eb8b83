"""
Authentication endpoints.

This module provides endpoints for user authentication including
login, registration, and token refresh with enhanced user schema support.
"""

from datetime import datetime, timezone
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>F<PERSON>
from beanie import PydanticObjectId

from app.models.user import User, MembershipDetails, ContributionMetrics, MembershipBenefits
from app.schemas.auth import Token, UserCreate
from app.schemas.user import UserResponse
from app.core.security import hash_password, verify_password, create_access_token

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate) -> UserResponse:
    """
    Register a new user with enhanced schema support.

    Args:
        user_data: User registration data

    Returns:
        UserResponse: Created user information

    Raises:
        HTTPException: If email is already registered
    """
    # Check if user already exists
    existing_user = await User.find_one(User.email == user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered"
        )

    # Hash password
    hashed_password = hash_password(user_data.password)

    # Get current time for timestamps
    current_time = datetime.now(timezone.utc)

    # Initialize membership details with defaults
    membership_details = MembershipDetails(
        joined_at=current_time,
        contributions=ContributionMetrics(),  # Default values (all 0)
        benefits=MembershipBenefits(),  # Default values (general access, no special rights)
    )

    # Create new user with enhanced schema
    user = User(
        email=user_data.email,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        preferred_name=user_data.preferred_name,
        bio=user_data.bio,
        avatar_url=user_data.avatar_url,
        # System roles - only allow admin to set non-default roles
        roles=user_data.roles if user_data.roles else ["user"],
        is_superuser=False,  # Never allow superuser creation via registration
        # Membership context
        membership_tier=user_data.membership_tier if user_data.membership_tier else "associate",
        membership_details=membership_details,
        tier_history=[],  # Empty initially
        # Meta fields
        created_at=current_time,
        updated_at=current_time,
        created_by=user_data.created_by,
        # Flexible fields
        tags=user_data.tags if user_data.tags else [],
        affiliations=user_data.affiliations if user_data.affiliations else [],
    )

    # Save to database
    await user.create()

    return UserResponse(
        id=str(user.id),
        email=user.email,
        full_name=user.full_name,
        preferred_name=user.preferred_name,
        bio=user.bio,
        avatar_url=user.avatar_url,
        roles=user.roles,
        is_superuser=user.is_superuser,
        membership_tier=user.membership_tier,
        membership_details=user.membership_details,
        tier_history=user.tier_history,
        is_active=user.is_active,
        is_verified=user.is_verified,
        created_at=user.created_at,
        updated_at=user.updated_at,
        created_by=user.created_by,
        tags=user.tags,
        affiliations=user.affiliations,
    )


@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()) -> Token:
    """
    Authenticate user and return access token.

    Args:
        form_data: OAuth2 password form data (username/email and password)

    Returns:
        Token: Access token and token type

    Raises:
        HTTPException: If credentials are invalid
    """
    # Find user by email
    user = await User.find_one(User.email == form_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is active
    if not user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")

    # Create access token
    access_token = create_access_token(data={"sub": str(user.id)})

    return Token(access_token=access_token, token_type="bearer")


@router.post("/refresh", response_model=Token)
async def refresh_token() -> Token:
    """
    Refresh access token using refresh token.

    Returns:
        Token: New access token and token type

    Note:
        This is a placeholder implementation. In production, you should
        implement proper refresh token validation.
    """
    # TODO: Implement proper refresh token logic
    # This is a placeholder implementation
    access_token = create_access_token(data={"sub": "placeholder"})

    return Token(access_token=access_token, token_type="bearer")
