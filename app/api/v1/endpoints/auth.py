"""
Authentication endpoints.

This module provides endpoints for user authentication including
login, registration, and token refresh.
"""

from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from beanie import PydanticObjectId

from app.models.user import User
from app.schemas.auth import Token, UserCreate
from app.schemas.user import UserResponse
from app.core.security import hash_password, verify_password, create_access_token

router = APIRouter()


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register(user_data: UserCreate) -> UserResponse:
    """
    Register a new user.

    Args:
        user_data: User registration data

    Returns:
        UserResponse: Created user information

    Raises:
        HTTPException: If email is already registered
    """
    # Check if user already exists
    existing_user = await User.find_one(User.email == user_data.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered"
        )

    # Create new user
    hashed_password = hash_password(user_data.password)
    user = User(
        email=user_data.email, full_name=user_data.full_name, hashed_password=hashed_password
    )

    # Save to database
    await user.create()

    return UserResponse(
        id=str(user.id), email=user.email, full_name=user.full_name, is_active=user.is_active
    )


@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()) -> Token:
    """
    Authenticate user and return access token.

    Args:
        form_data: OAuth2 password form data (username/email and password)

    Returns:
        Token: Access token and token type

    Raises:
        HTTPException: If credentials are invalid
    """
    # Find user by email
    user = await User.find_one(User.email == form_data.username)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Verify password
    if not verify_password(form_data.password, user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is active
    if not user.is_active:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")

    # Create access token
    access_token = create_access_token(data={"sub": str(user.id)})

    return Token(access_token=access_token, token_type="bearer")


@router.post("/refresh", response_model=Token)
async def refresh_token() -> Token:
    """
    Refresh access token using refresh token.

    Returns:
        Token: New access token and token type

    Note:
        This is a placeholder implementation. In production, you should
        implement proper refresh token validation.
    """
    # TODO: Implement proper refresh token logic
    # This is a placeholder implementation
    access_token = create_access_token(data={"sub": "placeholder"})

    return Token(access_token=access_token, token_type="bearer")
