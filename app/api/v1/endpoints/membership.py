"""
Membership management endpoints.

This module provides endpoints for managing user membership tiers,
contribution metrics, and tier history tracking.
"""

from datetime import datetime, timezone
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Path
from beanie import PydanticObjectId

from app.api.deps import (
    get_current_user,
    require_admin,
    require_superuser,
    require_partner_or_above,
    get_current_active_user
)
from app.models.user import User, TierHistoryEntry
from app.schemas.user import (
    UserResponse,
    MembershipTierUpdate,
    ContributionMetricsUpdate,
    UserRoleUpdate
)

router = APIRouter()


@router.put("/{user_id}/tier", response_model=UserResponse)
async def update_user_membership_tier(
    user_id: str = Path(..., description="User ID to update"),
    tier_update: MembershipTierUpdate = ...,
    current_admin: User = Depends(require_admin)
) -> UserResponse:
    """
    Update a user's membership tier (admin only).
    
    Args:
        user_id: ID of the user to update
        tier_update: New tier information
        current_admin: Current admin user
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If user is not found or update fails
    """
    try:
        # Get the user to update
        user_object_id = PydanticObjectId(user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Store old tier for history
        old_tier = user.membership_tier
        current_time = datetime.now(timezone.utc)
        
        # Create tier history entry
        tier_history_entry = TierHistoryEntry(
            tier=tier_update.tier,
            changed_at=current_time,
            changed_by=str(current_admin.id),
            reason=tier_update.reason
        )
        
        # Update user
        update_data = {
            "membership_tier": tier_update.tier,
            "updated_at": current_time,
            "tier_history": user.tier_history + [tier_history_entry]
        }
        
        # Update membership details if tier notes provided
        if tier_update.tier_notes:
            update_data["membership_details.tier_notes"] = tier_update.tier_notes
            update_data["membership_details.last_promoted_at"] = current_time
            update_data["membership_details.promoted_by"] = str(current_admin.id)
        
        await user.update({"$set": update_data})
        
        # Refresh user data
        user = await User.get(user_object_id)
        
        return UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            preferred_name=user.preferred_name,
            bio=user.bio,
            avatar_url=user.avatar_url,
            roles=user.roles,
            is_superuser=user.is_superuser,
            membership_tier=user.membership_tier,
            membership_details=user.membership_details,
            tier_history=user.tier_history,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            created_by=user.created_by,
            tags=user.tags,
            affiliations=user.affiliations
        )
        
    except Exception as e:
        if "User not found" in str(e):
            raise e
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update user membership tier"
        )


@router.put("/{user_id}/contributions", response_model=UserResponse)
async def update_user_contributions(
    user_id: str = Path(..., description="User ID to update"),
    contributions_update: ContributionMetricsUpdate = ...,
    current_user: User = Depends(require_partner_or_above)
) -> UserResponse:
    """
    Update a user's contribution metrics (partner tier or admin required).
    
    Args:
        user_id: ID of the user to update
        contributions_update: New contribution metrics
        current_user: Current authenticated user
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If user is not found or update fails
    """
    try:
        # Get the user to update
        user_object_id = PydanticObjectId(user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Prepare update data for nested contribution metrics
        update_data = {"updated_at": datetime.now(timezone.utc)}
        
        if contributions_update.projects_led is not None:
            update_data["membership_details.contributions.projects_led"] = contributions_update.projects_led
        if contributions_update.projects_participated is not None:
            update_data["membership_details.contributions.projects_participated"] = contributions_update.projects_participated
        if contributions_update.mentorships is not None:
            update_data["membership_details.contributions.mentorships"] = contributions_update.mentorships
        
        await user.update({"$set": update_data})
        
        # Refresh user data
        user = await User.get(user_object_id)
        
        return UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            preferred_name=user.preferred_name,
            bio=user.bio,
            avatar_url=user.avatar_url,
            roles=user.roles,
            is_superuser=user.is_superuser,
            membership_tier=user.membership_tier,
            membership_details=user.membership_details,
            tier_history=user.tier_history,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            created_by=user.created_by,
            tags=user.tags,
            affiliations=user.affiliations
        )
        
    except Exception as e:
        if "User not found" in str(e):
            raise e
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update user contributions"
        )


@router.put("/{user_id}/roles", response_model=UserResponse)
async def update_user_roles(
    user_id: str = Path(..., description="User ID to update"),
    role_update: UserRoleUpdate = ...,
    current_admin: User = Depends(require_superuser)
) -> UserResponse:
    """
    Update a user's system roles (superuser only).
    
    Args:
        user_id: ID of the user to update
        role_update: New role information
        current_admin: Current superuser
        
    Returns:
        UserResponse: Updated user information
        
    Raises:
        HTTPException: If user is not found or update fails
    """
    try:
        # Get the user to update
        user_object_id = PydanticObjectId(user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Update user roles
        update_data = {
            "roles": role_update.roles,
            "updated_at": datetime.now(timezone.utc)
        }
        
        await user.update({"$set": update_data})
        
        # Refresh user data
        user = await User.get(user_object_id)
        
        return UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            preferred_name=user.preferred_name,
            bio=user.bio,
            avatar_url=user.avatar_url,
            roles=user.roles,
            is_superuser=user.is_superuser,
            membership_tier=user.membership_tier,
            membership_details=user.membership_details,
            tier_history=user.tier_history,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            updated_at=user.updated_at,
            created_by=user.created_by,
            tags=user.tags,
            affiliations=user.affiliations
        )
        
    except Exception as e:
        if "User not found" in str(e):
            raise e
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to update user roles"
        )


@router.get("/{user_id}/tier-history")
async def get_user_tier_history(
    user_id: str = Path(..., description="User ID to get history for"),
    current_user: User = Depends(get_current_active_user)
) -> List[TierHistoryEntry]:
    """
    Get a user's tier change history.
    
    Users can view their own history, admins can view any user's history.
    
    Args:
        user_id: ID of the user to get history for
        current_user: Current authenticated user
        
    Returns:
        List[TierHistoryEntry]: User's tier change history
        
    Raises:
        HTTPException: If user is not found or access denied
    """
    try:
        # Get the user
        user_object_id = PydanticObjectId(user_id)
        user = await User.get(user_object_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check access permissions
        if str(current_user.id) != user_id and "admin" not in current_user.roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied. You can only view your own tier history."
            )
        
        return user.tier_history
        
    except Exception as e:
        if "User not found" in str(e) or "Access denied" in str(e):
            raise e
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Failed to retrieve tier history"
        )
