"""
API dependency injection utilities.

This module provides common dependencies used across API endpoints,
such as database access, authentication, and authorization.
"""

from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer

from app.core.database import get_database
from app.core.security import verify_token

# Security scheme for JWT token authentication
security = HTTPBearer()


async def get_current_user_id(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """
    Get current user ID from JWT token.

    Args:
        credentials: HTTP authorization credentials containing the JWT token

    Returns:
        str: User ID extracted from the token

    Raises:
        HTTPException: If token is invalid or expired
    """
    token = credentials.credentials
    payload = verify_token(token)

    if payload is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    user_id = payload.get("sub")
    if user_id is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token does not contain user ID",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user_id


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
) -> Optional[str]:
    """
    Get current user ID from JWT token (optional).

    This dependency allows endpoints to work with both authenticated and
    unauthenticated users.

    Args:
        credentials: Optional HTTP authorization credentials

    Returns:
        Optional[str]: User ID if token is valid, None otherwise
    """
    if credentials is None:
        return None

    token = credentials.credentials
    payload = verify_token(token)

    if payload is None:
        return None

    return payload.get("sub")


# Database dependency (for compatibility, though Beanie handles connections automatically)
get_database_session = get_database
