"""
Database configuration and session management with MongoDB.

This module sets up MongoDB connection using Motor and Beanie ODM.
"""

from typing import AsyncGenerator, List
from motor.motor_asyncio import AsyncIOMotor<PERSON>lient
from beanie import init_beanie
import asyncio

from app.core.config import get_settings


class Database:
    """MongoDB database connection manager."""

    client: AsyncIOMotorClient = None
    database = None


# Database instance
db = Database()


async def get_database():
    """
    Get database instance.

    This function provides database access for dependency injection.

    Returns:
        Database: MongoDB database instance
    """
    return db.database


async def connect_to_mongo():
    """
    Create database connection and initialize Beanie ODM.

    This function is called during application startup to establish
    connection to MongoDB and initialize the ODM with document models.
    """
    settings = get_settings()

    # Create MongoDB client
    db.client = AsyncIOMotorClient(settings.DATABASE_URL)
    db.database = db.client[settings.DATABASE_NAME]

    # Import all document models here to register them with Beanie
    from app.models.user import User

    # Initialize Beanie with document models
    await init_beanie(database=db.database, document_models=[User])

    print(f"🗄️  Connected to MongoDB database: {settings.DATABASE_NAME}")


async def close_mongo_connection():
    """
    Close database connection.

    This function is called during application shutdown to properly
    close the MongoDB connection.
    """
    if db.client:
        db.client.close()
        print("🔌 Disconnected from MongoDB")
