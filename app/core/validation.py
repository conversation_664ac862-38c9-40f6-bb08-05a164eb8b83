"""
Validation utilities for enhanced user schema.

This module provides validation functions for membership tier transitions,
role assignments, and other business logic constraints.
"""

from typing import List, Literal, Optional, Dict, Any
from datetime import datetime, timezone

from app.models.user import User, TierHistoryEntry


class ValidationError(Exception):
    """Custom exception for validation errors."""
    pass


class MembershipValidator:
    """Validator for membership-related operations."""
    
    # Define tier hierarchy for validation
    TIER_HIERARCHY = {
        "associate": 1,
        "partner": 2,
        "steward": 3
    }
    
    # Define valid tier transitions
    VALID_TRANSITIONS = {
        "associate": ["partner"],
        "partner": ["associate", "steward"],
        "steward": ["partner"]
    }
    
    # Define minimum contribution requirements for tier promotion
    TIER_REQUIREMENTS = {
        "partner": {
            "projects_led": 1,
            "projects_participated": 3,
            "mentorships": 0
        },
        "steward": {
            "projects_led": 3,
            "projects_participated": 5,
            "mentorships": 2
        }
    }
    
    @classmethod
    def validate_tier_transition(
        self,
        current_tier: Literal["associate", "partner", "steward"],
        new_tier: Literal["associate", "partner", "steward"],
        user_contributions: Optional[Dict[str, int]] = None,
        force: bool = False
    ) -> bool:
        """
        Validate if a tier transition is allowed.
        
        Args:
            current_tier: Current membership tier
            new_tier: Desired new tier
            user_contributions: User's contribution metrics
            force: Whether to force the transition (admin override)
            
        Returns:
            bool: True if transition is valid
            
        Raises:
            ValidationError: If transition is not allowed
        """
        if current_tier == new_tier:
            return True
            
        # Check if transition is structurally valid
        if new_tier not in self.VALID_TRANSITIONS.get(current_tier, []):
            if not force:
                raise ValidationError(
                    f"Invalid tier transition from {current_tier} to {new_tier}. "
                    f"Valid transitions from {current_tier}: {self.VALID_TRANSITIONS.get(current_tier, [])}"
                )
        
        # Check contribution requirements for promotions (not demotions)
        if (self.TIER_HIERARCHY[new_tier] > self.TIER_HIERARCHY[current_tier] 
            and not force and user_contributions):
            
            requirements = self.TIER_REQUIREMENTS.get(new_tier, {})
            for metric, required_value in requirements.items():
                user_value = user_contributions.get(metric, 0)
                if user_value < required_value:
                    raise ValidationError(
                        f"Insufficient {metric} for {new_tier} tier. "
                        f"Required: {required_value}, Current: {user_value}"
                    )
        
        return True
    
    @classmethod
    def validate_contribution_metrics(
        self,
        projects_led: Optional[int] = None,
        projects_participated: Optional[int] = None,
        mentorships: Optional[int] = None
    ) -> bool:
        """
        Validate contribution metrics values.
        
        Args:
            projects_led: Number of projects led
            projects_participated: Number of projects participated in
            mentorships: Number of mentorship activities
            
        Returns:
            bool: True if all metrics are valid
            
        Raises:
            ValidationError: If any metric is invalid
        """
        metrics = {
            "projects_led": projects_led,
            "projects_participated": projects_participated,
            "mentorships": mentorships
        }
        
        for metric_name, value in metrics.items():
            if value is not None:
                if not isinstance(value, int) or value < 0:
                    raise ValidationError(
                        f"Invalid {metric_name}: must be a non-negative integer"
                    )
        
        # Business logic validation
        if (projects_led is not None and projects_participated is not None 
            and projects_led > projects_participated):
            raise ValidationError(
                "Projects led cannot exceed projects participated"
            )
        
        return True
    
    @classmethod
    def get_tier_benefits(
        self,
        tier: Literal["associate", "partner", "steward"]
    ) -> Dict[str, Any]:
        """
        Get the default benefits for a membership tier.
        
        Args:
            tier: Membership tier
            
        Returns:
            Dict: Default benefits for the tier
        """
        benefits_by_tier = {
            "associate": {
                "tool_access_level": "general",
                "grant_eligibility": False,
                "visibility_boost": False,
                "governance_rights": "none"
            },
            "partner": {
                "tool_access_level": "beta",
                "grant_eligibility": True,
                "visibility_boost": True,
                "governance_rights": "voting"
            },
            "steward": {
                "tool_access_level": "co-dev",
                "grant_eligibility": True,
                "visibility_boost": True,
                "governance_rights": "council"
            }
        }
        
        return benefits_by_tier.get(tier, benefits_by_tier["associate"])


class RoleValidator:
    """Validator for role-related operations."""
    
    # Define valid system roles
    VALID_ROLES = ["user", "admin", "moderator", "staff"]
    
    # Define role hierarchy (higher number = more permissions)
    ROLE_HIERARCHY = {
        "user": 1,
        "staff": 2,
        "moderator": 3,
        "admin": 4
    }
    
    # Define role dependencies (roles that must be present with others)
    ROLE_DEPENDENCIES = {
        "admin": ["user"],  # Admins must also have user role
        "moderator": ["user"],  # Moderators must also have user role
        "staff": ["user"]  # Staff must also have user role
    }
    
    @classmethod
    def validate_roles(
        self,
        roles: List[Literal["user", "admin", "moderator", "staff"]]
    ) -> bool:
        """
        Validate a list of user roles.
        
        Args:
            roles: List of roles to validate
            
        Returns:
            bool: True if roles are valid
            
        Raises:
            ValidationError: If roles are invalid
        """
        if not roles:
            raise ValidationError("User must have at least one role")
        
        # Check for invalid roles
        invalid_roles = [role for role in roles if role not in self.VALID_ROLES]
        if invalid_roles:
            raise ValidationError(
                f"Invalid roles: {invalid_roles}. Valid roles: {self.VALID_ROLES}"
            )
        
        # Check for duplicate roles
        if len(roles) != len(set(roles)):
            raise ValidationError("Duplicate roles are not allowed")
        
        # Check role dependencies
        for role in roles:
            dependencies = self.ROLE_DEPENDENCIES.get(role, [])
            missing_deps = [dep for dep in dependencies if dep not in roles]
            if missing_deps:
                raise ValidationError(
                    f"Role '{role}' requires the following roles: {missing_deps}"
                )
        
        return True
    
    @classmethod
    def can_assign_roles(
        self,
        assigner_roles: List[str],
        target_roles: List[str],
        is_superuser: bool = False
    ) -> bool:
        """
        Check if a user can assign specific roles to another user.
        
        Args:
            assigner_roles: Roles of the user making the assignment
            target_roles: Roles being assigned
            is_superuser: Whether the assigner is a superuser
            
        Returns:
            bool: True if assignment is allowed
            
        Raises:
            ValidationError: If assignment is not allowed
        """
        if is_superuser:
            return True
        
        if "admin" not in assigner_roles:
            raise ValidationError("Only admins can assign roles")
        
        # Admins can assign any role except admin (unless they're superuser)
        admin_restricted_roles = ["admin"]
        restricted_roles = [role for role in target_roles if role in admin_restricted_roles]
        
        if restricted_roles and not is_superuser:
            raise ValidationError(
                f"Only superusers can assign these roles: {restricted_roles}"
            )
        
        return True


class UserValidator:
    """General user validation utilities."""
    
    @classmethod
    def validate_user_creation(
        self,
        email: str,
        roles: List[str],
        membership_tier: str,
        created_by: Optional[str] = None
    ) -> bool:
        """
        Validate user creation parameters.
        
        Args:
            email: User email
            roles: User roles
            membership_tier: User membership tier
            created_by: ID of user creating this user
            
        Returns:
            bool: True if parameters are valid
            
        Raises:
            ValidationError: If parameters are invalid
        """
        # Validate roles
        RoleValidator.validate_roles(roles)
        
        # Validate membership tier
        if membership_tier not in MembershipValidator.TIER_HIERARCHY:
            raise ValidationError(
                f"Invalid membership tier: {membership_tier}. "
                f"Valid tiers: {list(MembershipValidator.TIER_HIERARCHY.keys())}"
            )
        
        # Validate admin-only fields
        if (roles != ["user"] or membership_tier != "associate") and not created_by:
            raise ValidationError(
                "Non-default roles or membership tier require admin creation"
            )
        
        return True
    
    @classmethod
    def validate_tags(self, tags: List[str]) -> bool:
        """
        Validate user tags.
        
        Args:
            tags: List of user tags
            
        Returns:
            bool: True if tags are valid
            
        Raises:
            ValidationError: If tags are invalid
        """
        if not isinstance(tags, list):
            raise ValidationError("Tags must be a list")
        
        # Check for empty or invalid tags
        for tag in tags:
            if not isinstance(tag, str) or not tag.strip():
                raise ValidationError("All tags must be non-empty strings")
            if len(tag) > 50:
                raise ValidationError("Tags must be 50 characters or less")
        
        # Check for duplicates
        if len(tags) != len(set(tags)):
            raise ValidationError("Duplicate tags are not allowed")
        
        return True
