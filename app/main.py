"""
FastAPI application main module.

This module creates and configures the FastAPI application instance,
sets up middleware, includes routers, and defines the application lifecycle.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from app.api.v1.api import api_router
from app.core.config import get_settings
from app.core.database import connect_to_mongo, close_mongo_connection


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan context manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    await connect_to_mongo()
    print("🚀 Sainpse Backend started successfully!")

    yield

    # Shutdown
    await close_mongo_connection()
    print("📴 Sainpse Backend shutting down...")


def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application.

    Returns:
        FastAPI: Configured FastAPI application instance
    """
    settings = get_settings()

    app = FastAPI(
        title="Sainpse Backend API",
        description="A modern FastAPI backend application for the Sainpse project",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=lifespan,
    )

    # Set up CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_HOSTS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include API router
    app.include_router(api_router, prefix="/api/v1")

    @app.get("/", response_class=JSONResponse)
    async def root() -> dict[str, str]:
        """Root endpoint returning API information."""
        return {
            "message": "Welcome to Sainpse Backend API",
            "version": "1.0.0",
            "docs": "/docs",
            "redoc": "/redoc",
        }

    @app.get("/health", response_class=JSONResponse)
    async def health_check() -> dict[str, str]:
        """Health check endpoint."""
        return {"status": "healthy", "service": "sainpse-backend"}

    return app


# Create the application instance
app = create_application()

if __name__ == "__main__":
    import uvicorn

    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
