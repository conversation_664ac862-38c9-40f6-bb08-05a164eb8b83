"""
Authentication-related Pydantic schemas.

This module defines the data models for authentication API requests and responses.
"""

from pydantic import BaseModel, EmailStr, Field, ConfigDict

from app.schemas.user import UserCreate


class UserLogin(BaseModel):
    """Schema for user login requests."""

    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"email": "<EMAIL>", "password": "securepassword123"}
        }
    )


class Token(BaseModel):
    """Schema for authentication token responses."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                "token_type": "bearer",
            }
        }
    )


class TokenData(BaseModel):
    """Schema for token payload data."""

    user_id: str = Field(..., description="User identifier from token")


class RefreshToken(BaseModel):
    """Schema for refresh token requests."""

    refresh_token: str = Field(..., description="JWT refresh token")

    model_config = ConfigDict(
        json_schema_extra={"example": {"refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."}}
    )


# Re-export UserCreate for convenience
__all__ = ["UserLogin", "Token", "TokenData", "RefreshToken", "UserCreate"]
