"""
User-related Pydantic schemas.

This module defines the data models for user-related API requests and responses
with enhanced membership and role management capabilities.
"""

from datetime import datetime
from typing import Optional, List, Literal

from pydantic import BaseModel, EmailStr, Field, ConfigDict

# Import nested models from the main User model
from app.models.user import (
    ContributionMetrics,
    MembershipBenefits,
    MembershipDetails,
    TierHistoryEntry,
)


class UserBase(BaseModel):
    """Base user schema with common fields."""

    email: EmailStr = Field(..., description="User email address")
    full_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="User full name"
    )
    preferred_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="User preferred name"
    )
    bio: Optional[str] = Field(None, max_length=500, description="User biography")
    avatar_url: Optional[str] = Field(None, description="URL to user's avatar image")


class UserCreate(UserBase):
    """Schema for user creation requests."""

    password: str = Field(..., min_length=8, max_length=100, description="User password")

    # Optional fields for user creation
    roles: Optional[List[Literal["user", "admin", "moderator", "staff"]]] = Field(
        default=None, description="System roles (admin only)"
    )
    membership_tier: Optional[Literal["associate", "partner", "steward"]] = Field(
        default=None, description="Initial membership tier (admin only)"
    )
    tags: Optional[List[str]] = Field(default=None, description="User tags")
    affiliations: Optional[List[str]] = Field(default=None, description="User affiliations")
    created_by: Optional[str] = Field(default=None, description="ID of admin creating this user")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "preferred_name": "John",
                "password": "securepassword123",
                "bio": "Software developer interested in AI cooperation",
                "tags": ["developer", "ai-enthusiast"],
                "affiliations": ["AI Co-op"],
            }
        }
    )


class UserUpdate(BaseModel):
    """Schema for user update requests."""

    full_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="User full name"
    )
    preferred_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="User preferred name"
    )
    bio: Optional[str] = Field(None, max_length=500, description="User biography")
    avatar_url: Optional[str] = Field(None, description="URL to user's avatar image")
    tags: Optional[List[str]] = Field(None, description="User tags")
    affiliations: Optional[List[str]] = Field(None, description="User affiliations")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "full_name": "John Smith",
                "preferred_name": "Johnny",
                "bio": "Updated bio with new interests",
                "tags": ["developer", "ai-researcher"],
                "affiliations": ["AI Co-op", "Tech Guild"],
            }
        }
    )


class UserResponse(UserBase):
    """Schema for user response data."""

    id: str = Field(..., description="User unique identifier")
    roles: List[Literal["user", "admin", "moderator", "staff"]] = Field(
        ..., description="System roles assigned to the user"
    )
    is_superuser: bool = Field(..., description="Whether user has superuser privileges")
    membership_tier: Literal["associate", "partner", "steward"] = Field(
        ..., description="Current membership tier"
    )
    membership_details: MembershipDetails = Field(
        ..., description="Detailed membership information"
    )
    tier_history: List[TierHistoryEntry] = Field(..., description="History of tier changes")
    is_active: bool = Field(..., description="Whether the user account is active")
    is_verified: bool = Field(..., description="Whether the user email is verified")
    created_at: datetime = Field(..., description="When the user account was created")
    updated_at: datetime = Field(..., description="When the user account was last updated")
    created_by: Optional[str] = Field(None, description="ID of admin who created this user")
    tags: List[str] = Field(..., description="Tags associated with the user")
    affiliations: List[str] = Field(..., description="User's organizational affiliations")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "preferred_name": "John",
                "bio": "Software developer interested in AI cooperation",
                "avatar_url": "https://example.com/avatar.jpg",
                "roles": ["user"],
                "is_superuser": False,
                "membership_tier": "associate",
                "is_active": True,
                "is_verified": True,
                "tags": ["developer", "ai-enthusiast"],
                "affiliations": ["AI Co-op"],
            }
        },
    )


class UserInDB(UserBase):
    """Schema for user data stored in database."""

    id: str
    hashed_password: str
    roles: List[Literal["user", "admin", "moderator", "staff"]]
    is_superuser: bool
    membership_tier: Literal["associate", "partner", "steward"]
    membership_details: MembershipDetails
    tier_history: List[TierHistoryEntry]
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    tags: List[str]
    affiliations: List[str]

    model_config = ConfigDict(from_attributes=True)


# Additional schemas for membership management


class MembershipTierUpdate(BaseModel):
    """Schema for updating user membership tier."""

    tier: Literal["associate", "partner", "steward"] = Field(..., description="New membership tier")
    reason: Optional[str] = Field(None, description="Reason for tier change")
    tier_notes: Optional[str] = Field(None, description="Notes about the tier assignment")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "tier": "partner",
                "reason": "Demonstrated leadership in multiple projects",
                "tier_notes": "Promoted after successful completion of Project Alpha",
            }
        }
    )


class ContributionMetricsUpdate(BaseModel):
    """Schema for updating user contribution metrics."""

    projects_led: Optional[int] = Field(None, ge=0, description="Number of projects led")
    projects_participated: Optional[int] = Field(
        None, ge=0, description="Number of projects participated in"
    )
    mentorships: Optional[int] = Field(None, ge=0, description="Number of mentorship activities")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"projects_led": 2, "projects_participated": 5, "mentorships": 1}
        }
    )


class UserRoleUpdate(BaseModel):
    """Schema for updating user system roles."""

    roles: List[Literal["user", "admin", "moderator", "staff"]] = Field(
        ..., description="New system roles for the user"
    )

    model_config = ConfigDict(json_schema_extra={"example": {"roles": ["user", "moderator"]}})
