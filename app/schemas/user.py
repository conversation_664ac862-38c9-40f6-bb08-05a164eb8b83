"""
User-related Pydantic schemas.

This module defines the data models for user-related API requests and responses.
"""

from typing import Optional

from pydantic import BaseModel, EmailStr, Field, ConfigDict


class UserBase(BaseModel):
    """Base user schema with common fields."""

    email: EmailStr = Field(..., description="User email address")
    full_name: str = Field(..., min_length=1, max_length=100, description="User full name")


class UserCreate(UserBase):
    """Schema for user creation requests."""

    password: str = Field(..., min_length=8, max_length=100, description="User password")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "email": "<EMAIL>",
                "full_name": "<PERSON>",
                "password": "securepassword123",
            }
        }
    )


class UserUpdate(BaseModel):
    """Schema for user update requests."""

    full_name: Optional[str] = Field(
        None, min_length=1, max_length=100, description="User full name"
    )

    model_config = ConfigDict(json_schema_extra={"example": {"full_name": "<PERSON>"}})


class UserResponse(UserBase):
    """Schema for user response data."""

    id: str = Field(..., description="User unique identifier")
    is_active: bool = Field(..., description="Whether the user account is active")

    model_config = ConfigDict(
        from_attributes=True,
        json_schema_extra={
            "example": {
                "id": "123e4567-e89b-12d3-a456-************",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "is_active": True,
            }
        },
    )


class UserInDB(UserBase):
    """Schema for user data stored in database."""

    id: str
    hashed_password: str
    is_active: bool

    model_config = ConfigDict(from_attributes=True)
