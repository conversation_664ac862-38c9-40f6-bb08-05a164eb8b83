"""
Database migration script for enhanced User schema.

This script migrates existing user documents to the new enhanced schema
with membership management and role-based access control.
"""

import asyncio
from datetime import datetime, timezone
from typing import List, Dict, Any

from motor.motor_asyncio import AsyncIOMotorClient
from pymongo import UpdateOne

from app.core.config import get_settings
from app.models.user import ContributionMetrics, MembershipBenefits, MembershipDetails


async def migrate_user_schema():
    """
    Migrate existing user documents to the new enhanced schema.
    
    This function:
    1. Connects to MongoDB
    2. Finds all existing user documents
    3. Updates them with new required fields and default values
    4. Creates necessary indexes
    """
    settings = get_settings()
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(settings.DATABASE_URL)
    db = client[settings.DATABASE_NAME]
    users_collection = db.users
    
    print("🔄 Starting user schema migration...")
    
    # Get all existing users
    existing_users = await users_collection.find({}).to_list(length=None)
    print(f"📊 Found {len(existing_users)} existing users to migrate")
    
    if not existing_users:
        print("✅ No existing users found. Migration complete.")
        return
    
    # Prepare bulk operations
    bulk_operations: List[UpdateOne] = []
    
    for user in existing_users:
        user_id = user["_id"]
        current_time = datetime.now(timezone.utc)
        
        # Prepare update document with new fields
        update_doc = {
            "$set": {
                # New optional fields with defaults
                "preferred_name": user.get("preferred_name"),  # Keep existing if present
                "roles": user.get("roles", ["user"]),  # Default to ["user"]
                "is_superuser": user.get("is_superuser", False),  # Default to False
                "membership_tier": user.get("membership_tier", "associate"),  # Default to "associate"
                "tier_history": user.get("tier_history", []),  # Default to empty list
                "created_by": user.get("created_by"),  # Keep existing if present
                "tags": user.get("tags", []),  # Default to empty list
                "affiliations": user.get("affiliations", []),  # Default to empty list
                
                # Initialize membership_details if not present
                "membership_details": user.get("membership_details", {
                    "joined_at": user.get("created_at", current_time),
                    "last_promoted_at": None,
                    "promoted_by": None,
                    "tier_notes": None,
                    "contributions": {
                        "projects_led": 0,
                        "projects_participated": 0,
                        "mentorships": 0
                    },
                    "benefits": {
                        "tool_access_level": "general",
                        "grant_eligibility": False,
                        "visibility_boost": False,
                        "governance_rights": "none"
                    }
                }),
                
                # Update timestamp
                "updated_at": current_time
            }
        }
        
        # Add to bulk operations
        bulk_operations.append(UpdateOne({"_id": user_id}, update_doc))
    
    # Execute bulk update
    if bulk_operations:
        result = await users_collection.bulk_write(bulk_operations)
        print(f"✅ Updated {result.modified_count} user documents")
    
    # Create new indexes for enhanced schema
    print("🔍 Creating indexes for enhanced schema...")
    
    indexes_to_create = [
        ("membership_tier", 1),
        ("roles", 1),
        ("is_superuser", 1),
        ("is_active", 1),
        ("tags", 1),
    ]
    
    for index_spec in indexes_to_create:
        try:
            await users_collection.create_index([index_spec])
            print(f"✅ Created index on {index_spec[0]}")
        except Exception as e:
            print(f"⚠️  Index on {index_spec[0]} may already exist: {e}")
    
    # Close connection
    client.close()
    print("🎉 User schema migration completed successfully!")


async def rollback_user_schema():
    """
    Rollback the user schema migration (remove new fields).
    
    WARNING: This will remove all new fields and data!
    Use only in development or if migration needs to be reversed.
    """
    settings = get_settings()
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(settings.DATABASE_URL)
    db = client[settings.DATABASE_NAME]
    users_collection = db.users
    
    print("🔄 Starting user schema rollback...")
    
    # Remove new fields from all user documents
    rollback_result = await users_collection.update_many(
        {},
        {
            "$unset": {
                "preferred_name": "",
                "roles": "",
                "is_superuser": "",
                "membership_tier": "",
                "membership_details": "",
                "tier_history": "",
                "created_by": "",
                "tags": "",
                "affiliations": ""
            }
        }
    )
    
    print(f"✅ Rolled back {rollback_result.modified_count} user documents")
    
    # Drop new indexes
    indexes_to_drop = [
        "membership_tier_1",
        "roles_1", 
        "is_superuser_1",
        "is_active_1",
        "tags_1"
    ]
    
    for index_name in indexes_to_drop:
        try:
            await users_collection.drop_index(index_name)
            print(f"✅ Dropped index {index_name}")
        except Exception as e:
            print(f"⚠️  Could not drop index {index_name}: {e}")
    
    # Close connection
    client.close()
    print("🎉 User schema rollback completed!")


async def verify_migration():
    """
    Verify that the migration was successful by checking a sample of users.
    """
    settings = get_settings()
    
    # Connect to MongoDB
    client = AsyncIOMotorClient(settings.DATABASE_URL)
    db = client[settings.DATABASE_NAME]
    users_collection = db.users
    
    print("🔍 Verifying migration...")
    
    # Check a few users to ensure they have the new fields
    sample_users = await users_collection.find({}).limit(5).to_list(length=5)
    
    required_fields = [
        "roles", "is_superuser", "membership_tier", "membership_details",
        "tier_history", "tags", "affiliations"
    ]
    
    for user in sample_users:
        user_email = user.get("email", "unknown")
        missing_fields = [field for field in required_fields if field not in user]
        
        if missing_fields:
            print(f"❌ User {user_email} is missing fields: {missing_fields}")
        else:
            print(f"✅ User {user_email} has all required fields")
    
    # Check indexes
    indexes = await users_collection.list_indexes().to_list(length=None)
    index_names = [idx["name"] for idx in indexes]
    
    expected_indexes = ["membership_tier_1", "roles_1", "is_superuser_1"]
    for expected_index in expected_indexes:
        if expected_index in index_names:
            print(f"✅ Index {expected_index} exists")
        else:
            print(f"❌ Index {expected_index} is missing")
    
    # Close connection
    client.close()
    print("🎉 Migration verification completed!")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "migrate":
            asyncio.run(migrate_user_schema())
        elif command == "rollback":
            asyncio.run(rollback_user_schema())
        elif command == "verify":
            asyncio.run(verify_migration())
        else:
            print("Usage: python migrate_user_schema.py [migrate|rollback|verify]")
    else:
        print("Usage: python migrate_user_schema.py [migrate|rollback|verify]")
