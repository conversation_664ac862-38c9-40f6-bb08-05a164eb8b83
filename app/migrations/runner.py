"""
Migration runner utility for database schema changes.

This module provides utilities to run migrations safely and track their status.
"""

import asyncio
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

from motor.motor_asyncio import AsyncIOMotorClient

from app.core.config import get_settings


class MigrationRunner:
    """Utility class for running and tracking database migrations."""
    
    def __init__(self):
        self.settings = get_settings()
        self.client: Optional[AsyncIOMotorClient] = None
        self.db = None
        
    async def connect(self):
        """Connect to MongoDB."""
        self.client = AsyncIOMotorClient(self.settings.DATABASE_URL)
        self.db = self.client[self.settings.DATABASE_NAME]
        
    async def disconnect(self):
        """Disconnect from MongoDB."""
        if self.client:
            self.client.close()
            
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
        
    async def create_migration_tracking(self):
        """Create the migrations tracking collection if it doesn't exist."""
        migrations_collection = self.db.migrations
        
        # Check if collection exists
        collections = await self.db.list_collection_names()
        if "migrations" not in collections:
            await migrations_collection.create_index("name", unique=True)
            print("✅ Created migrations tracking collection")
            
    async def is_migration_applied(self, migration_name: str) -> bool:
        """Check if a migration has already been applied."""
        migrations_collection = self.db.migrations
        result = await migrations_collection.find_one({"name": migration_name})
        return result is not None
        
    async def record_migration(self, migration_name: str, description: str = ""):
        """Record that a migration has been applied."""
        migrations_collection = self.db.migrations
        
        migration_record = {
            "name": migration_name,
            "description": description,
            "applied_at": datetime.now(timezone.utc),
            "status": "completed"
        }
        
        await migrations_collection.insert_one(migration_record)
        print(f"✅ Recorded migration: {migration_name}")
        
    async def remove_migration_record(self, migration_name: str):
        """Remove a migration record (for rollbacks)."""
        migrations_collection = self.db.migrations
        result = await migrations_collection.delete_one({"name": migration_name})
        
        if result.deleted_count > 0:
            print(f"✅ Removed migration record: {migration_name}")
        else:
            print(f"⚠️  Migration record not found: {migration_name}")
            
    async def list_applied_migrations(self) -> List[Dict[str, Any]]:
        """List all applied migrations."""
        migrations_collection = self.db.migrations
        migrations = await migrations_collection.find({}).sort("applied_at", 1).to_list(length=None)
        return migrations
        
    async def run_migration_safely(self, migration_name: str, migration_func, description: str = ""):
        """
        Run a migration safely with tracking and error handling.
        
        Args:
            migration_name: Unique name for the migration
            migration_func: Async function that performs the migration
            description: Description of what the migration does
        """
        await self.create_migration_tracking()
        
        # Check if migration already applied
        if await self.is_migration_applied(migration_name):
            print(f"⏭️  Migration {migration_name} already applied, skipping")
            return
            
        print(f"🔄 Running migration: {migration_name}")
        
        try:
            # Run the migration
            await migration_func()
            
            # Record successful migration
            await self.record_migration(migration_name, description)
            print(f"✅ Migration {migration_name} completed successfully")
            
        except Exception as e:
            print(f"❌ Migration {migration_name} failed: {e}")
            raise


async def run_user_schema_migration():
    """Run the user schema migration using the migration runner."""
    from app.migrations.migrate_user_schema import migrate_user_schema
    
    async with MigrationRunner() as runner:
        await runner.run_migration_safely(
            migration_name="enhance_user_schema_v1",
            migration_func=migrate_user_schema,
            description="Add membership management and role-based access control to User model"
        )


async def rollback_user_schema_migration():
    """Rollback the user schema migration."""
    from app.migrations.migrate_user_schema import rollback_user_schema
    
    async with MigrationRunner() as runner:
        # Run the rollback
        await rollback_user_schema()
        
        # Remove the migration record
        await runner.remove_migration_record("enhance_user_schema_v1")


async def list_migrations():
    """List all applied migrations."""
    async with MigrationRunner() as runner:
        migrations = await runner.list_applied_migrations()
        
        if not migrations:
            print("📝 No migrations have been applied yet")
            return
            
        print("📝 Applied migrations:")
        for migration in migrations:
            applied_at = migration["applied_at"].strftime("%Y-%m-%d %H:%M:%S UTC")
            print(f"  • {migration['name']} - {applied_at}")
            if migration.get("description"):
                print(f"    {migration['description']}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "migrate":
            asyncio.run(run_user_schema_migration())
        elif command == "rollback":
            asyncio.run(rollback_user_schema_migration())
        elif command == "list":
            asyncio.run(list_migrations())
        else:
            print("Usage: python runner.py [migrate|rollback|list]")
    else:
        print("Usage: python runner.py [migrate|rollback|list]")
