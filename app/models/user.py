"""
User document model for MongoDB.

This module defines the Beanie document model for the User entity with enhanced
membership and role management capabilities.
"""

from datetime import datetime, timezone
from typing import Optional, List, Literal
from pydantic import BaseModel, EmailStr, Field
from beanie import Document


class ContributionMetrics(BaseModel):
    """User contribution metrics for membership evaluation."""

    projects_led: int = Field(default=0, description="Number of projects led by the user")
    projects_participated: int = Field(default=0, description="Number of projects participated in")
    mentorships: int = Field(default=0, description="Number of mentorship activities")


class MembershipBenefits(BaseModel):
    """Benefits associated with user's membership tier."""

    tool_access_level: Literal["general", "beta", "co-dev"] = Field(
        default="general", description="Level of tool access granted"
    )
    grant_eligibility: bool = Field(default=False, description="Eligibility for grants")
    visibility_boost: bool = Field(default=False, description="Enhanced profile visibility")
    governance_rights: Literal["none", "voting", "council", "advisory"] = Field(
        default="none", description="Governance participation rights"
    )


class MembershipDetails(BaseModel):
    """Detailed membership information and history."""

    joined_at: datetime = Field(description="When the user joined the membership program")
    last_promoted_at: Optional[datetime] = Field(default=None, description="Last promotion date")
    promoted_by: Optional[str] = Field(
        default=None, description="ID of user who promoted this member"
    )
    tier_notes: Optional[str] = Field(default=None, description="Notes about tier assignment")
    contributions: ContributionMetrics = Field(
        default_factory=ContributionMetrics, description="User's contribution metrics"
    )
    benefits: MembershipBenefits = Field(
        default_factory=MembershipBenefits, description="Current membership benefits"
    )


class TierHistoryEntry(BaseModel):
    """Historical record of tier changes."""

    tier: Literal["associate", "partner", "steward"] = Field(description="Membership tier")
    changed_at: datetime = Field(description="When the tier change occurred")
    changed_by: str = Field(description="ID of user who made the change")
    reason: Optional[str] = Field(default=None, description="Reason for tier change")


class User(Document):
    """
    Enhanced User document model.

    Represents a user in the system with authentication, profile information,
    role-based access control, and membership management.
    """

    # Core identity
    email: EmailStr = Field(..., unique=True, index=True, description="User email address")
    hashed_password: str = Field(..., description="Hashed password for authentication")
    full_name: Optional[str] = Field(default=None, description="User's full name")
    preferred_name: Optional[str] = Field(default=None, description="User's preferred name")
    avatar_url: Optional[str] = Field(default=None, description="URL to user's avatar image")
    bio: Optional[str] = Field(default=None, description="User's biography")

    # System-level roles and flags
    roles: List[Literal["user", "admin", "moderator", "staff"]] = Field(
        default=["user"], description="System roles assigned to the user"
    )
    is_superuser: bool = Field(default=False, description="Grants full bypass rights if True")

    # Membership context
    membership_tier: Literal["associate", "partner", "steward"] = Field(
        default="associate", description="Current membership tier"
    )
    membership_details: MembershipDetails = Field(description="Detailed membership information")
    tier_history: List[TierHistoryEntry] = Field(default=[], description="History of tier changes")

    # Meta fields
    is_active: bool = Field(default=True, description="Whether the user account is active")
    is_verified: bool = Field(default=False, description="Whether the user email is verified")
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the user account was created",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="When the user account was last updated",
    )
    created_by: Optional[str] = Field(
        default=None, description="ID of admin/staff who registered this user"
    )

    # Flexible context fields
    tags: List[str] = Field(default=[], description="Tags associated with the user")
    affiliations: List[str] = Field(
        default=[], description="Organizations or groups the user is affiliated with"
    )

    class Settings:
        """Beanie document settings."""

        name = "users"  # Collection name in MongoDB
        indexes = [
            "email",  # Index on email field
            "membership_tier",  # Index for membership queries
            "roles",  # Index for role-based queries
            "is_superuser",  # Index for admin queries
            "is_active",  # Index for active user queries
        ]

    def __repr__(self) -> str:
        """String representation of the User document."""
        display_name = self.preferred_name or self.full_name or self.email
        return f"<User(id={self.id}, email={self.email}, name={display_name}, tier={self.membership_tier})>"
