"""
User document model for MongoDB.

This module defines the Beanie document model for the User entity.
"""

from datetime import datetime
from typing import Optional
from pydantic import Field
from beanie import Document


class User(Document):
    """
    User document model.

    Represents a user in the system with authentication and profile information.
    """

    # Authentication fields
    email: str = Field(..., unique=True, index=True)
    hashed_password: str = Field(...)

    # Profile fields
    full_name: str = Field(...)

    # Status fields
    is_active: bool = Field(default=True)
    is_verified: bool = Field(default=False)

    # Timestamp fields
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Optional fields
    bio: Optional[str] = Field(default=None)
    avatar_url: Optional[str] = Field(default=None)

    class Settings:
        """Beanie document settings."""

        name = "users"  # Collection name in MongoDB
        indexes = [
            "email",  # Index on email field
        ]

    def __repr__(self) -> str:
        """String representation of the User document."""
        return f"<User(id={self.id}, email={self.email}, full_name={self.full_name})>"
