# API Testing Documentation

This document explains how to test the authentication endpoints in the Sainpse Backend API.

## Test Files

### 1. `tests/test_endpoints.py`
Comprehensive pytest test suite for authentication endpoints.

**Features:**
- User registration testing (valid/invalid scenarios)
- User login testing (valid/invalid credentials)
- Token validation testing
- Protected endpoint access testing
- Integration tests for complete auth flow
- Async endpoint testing

**Run the tests:**
```bash
# Run all endpoint tests
pytest tests/test_endpoints.py -v

# Run specific test class
pytest tests/test_endpoints.py::TestUserRegistration -v

# Run specific test
pytest tests/test_endpoints.py::TestUserLogin::test_login_valid_credentials -v
```

### 2. `test_endpoints_manual.py`
Interactive manual testing script for live API testing.

**Features:**
- Health check testing
- User registration with real HTTP requests
- User login with real HTTP requests
- Protected endpoint access testing
- Token refresh testing
- Comprehensive test summary

**Run the manual tests:**
```bash
# Test against default localhost:8000
python test_endpoints_manual.py

# Test against custom URL
python test_endpoints_manual.py http://your-api-url.com
```

## Test Coverage

The test suite covers:

### ✅ User Registration
- Valid user registration
- Duplicate email handling
- Invalid email format validation
- Password strength validation
- Required field validation

### ✅ User Authentication
- Valid login credentials
- Invalid email handling
- Invalid password handling
- Inactive user handling
- Missing credentials validation

### ✅ Token Management
- Valid token access to protected endpoints
- Invalid token rejection
- Expired token handling
- Missing token handling
- Token refresh functionality

### ✅ Integration Testing
- Complete authentication flow
- Error handling scenarios
- Async endpoint testing

## API Endpoints Tested

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/health` | GET | Health check endpoint |
| `/` | GET | Root endpoint |
| `/api/v1/auth/register` | POST | User registration |
| `/api/v1/auth/login` | POST | User login |
| `/api/v1/auth/refresh` | POST | Token refresh |
| `/api/v1/users/me` | GET | Get current user (protected) |

## Running the Server

Before running tests, ensure the server is running:

```bash
# Using the configured task
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Or using the VS Code task
# Run the "start-server" task
```

## Test Environment

The tests use:
- **pytest** for test framework
- **httpx** for async HTTP client
- **FastAPI TestClient** for sync testing
- **mongomock** for database mocking
- **Beanie** for MongoDB ODM

## Example Test Usage

### Manual Testing Example:
```python
from test_endpoints_manual import APITester

# Create tester instance
tester = APITester("http://localhost:8000")

# Test individual endpoints
tester.test_health_check()
tester.register_user("<EMAIL>", "Test User", "password123")
tester.login_user("<EMAIL>", "password123")
tester.test_protected_endpoint()

# Or run all tests
tester.run_all_tests()
```

### Pytest Example:
```bash
# Run all tests with coverage
pytest tests/test_endpoints.py --cov=app

# Run with detailed output
pytest tests/test_endpoints.py -v -s

# Run specific test categories
pytest tests/test_endpoints.py -k "registration"
pytest tests/test_endpoints.py -k "login"
pytest tests/test_endpoints.py -k "token"
```

## Expected Test Results

When everything is working correctly, you should see:
- ✅ All registration tests pass
- ✅ All login tests pass
- ✅ All token validation tests pass
- ✅ Integration tests pass
- ✅ Manual tests show all endpoints working

## Troubleshooting

### Common Issues:

1. **Server not running**: Ensure FastAPI server is running on port 8000
2. **Database connection**: Check MongoDB connection in `.env` file
3. **Import errors**: Ensure all dependencies are installed (`pip install -r requirements.txt`)
4. **Test database**: Tests use mongomock, no real database connection needed

### Debug Mode:

For detailed debugging, run tests with:
```bash
pytest tests/test_endpoints.py -v -s --tb=long
```

## Security Notes

The tests verify:
- Passwords are properly hashed (not stored in plain text)
- JWT tokens are properly generated and validated
- Protected endpoints require valid authentication
- Invalid tokens are properly rejected
- User status (active/inactive) is properly checked
