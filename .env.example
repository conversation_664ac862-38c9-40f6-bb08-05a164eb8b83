# Application settings
APP_NAME=Sainpse
APP_VERSION=1.0.0
DEBUG=True
ENVIRONMENT=development

# Server settings
HOST=0.0.0.0
PORT=8000
RELOAD=True

# Security settings
SECRET_KEY=your-super-secret-key-change-this-in-production-please
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS settings
ALLOWED_HOSTS=["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000"]

# Database settings
DATABASE_URL=mongodb+srv://MarcusMadumo:<EMAIL>/?retryWrites=true&w=majority&appName=Sainpse
DATABASE_NAME=sainpse_db

# Redis settings
REDIS_URL=redis://localhost:6379/0

# Email settings (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=True
