#!/usr/bin/env python3
"""
Manual endpoint testing script.

This script provides a simple way to manually test the authentication endpoints
using real HTTP requests. Run this script to test the API endpoints interactively.
"""

import requests
import json
from typing import Optional


class APITester:
    """Simple API testing class."""

    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.access_token: Optional[str] = None

    def test_health_check(self) -> bool:
        """Test the health check endpoint."""
        print("🔍 Testing health check endpoint...")
        try:
            response = requests.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed!")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Health check failed with status: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check failed with error: {e}")
            return False

    def test_root_endpoint(self) -> bool:
        """Test the root endpoint."""
        print("\n🔍 Testing root endpoint...")
        try:
            response = requests.get(f"{self.base_url}/")
            if response.status_code == 200:
                print("✅ Root endpoint working!")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Root endpoint failed with status: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Root endpoint failed with error: {e}")
            return False

    def register_user(self, email: str, full_name: str, password: str) -> bool:
        """Register a new user."""
        print(f"\n🔍 Testing user registration for {email}...")
        try:
            user_data = {"email": email, "full_name": full_name, "password": password}

            response = requests.post(f"{self.base_url}/api/v1/auth/register", json=user_data)

            if response.status_code == 201:
                print("✅ User registration successful!")
                user_info = response.json()
                print(f"   User ID: {user_info['id']}")
                print(f"   Email: {user_info['email']}")
                print(f"   Full Name: {user_info['full_name']}")
                return True
            elif response.status_code == 400:
                print(f"⚠️  User registration failed: {response.json()['detail']}")
                return False
            else:
                print(f"❌ User registration failed with status: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ User registration failed with error: {e}")
            return False

    def login_user(self, email: str, password: str) -> bool:
        """Login a user and store the access token."""
        print(f"\n🔍 Testing user login for {email}...")
        try:
            login_data = {"username": email, "password": password}  # OAuth2 uses 'username' field

            response = requests.post(f"{self.base_url}/api/v1/auth/login", data=login_data)

            if response.status_code == 200:
                print("✅ User login successful!")
                token_data = response.json()
                self.access_token = token_data["access_token"]
                print(f"   Token type: {token_data['token_type']}")
                print(f"   Access token: {self.access_token[:20]}...")
                return True
            else:
                print(f"❌ User login failed with status: {response.status_code}")
                print(f"   Response: {response.json()}")
                return False
        except Exception as e:
            print(f"❌ User login failed with error: {e}")
            return False

    def test_protected_endpoint(self) -> bool:
        """Test a protected endpoint (if it exists)."""
        print(f"\n🔍 Testing protected endpoint...")
        if not self.access_token:
            print("❌ No access token available. Please login first.")
            return False

        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            response = requests.get(f"{self.base_url}/api/v1/users/me", headers=headers)

            if response.status_code == 200:
                print("✅ Protected endpoint accessible!")
                print(f"   Response: {response.json()}")
                return True
            elif response.status_code == 404:
                print("⚠️  Protected endpoint not found (endpoint may not be implemented yet)")
                return True  # This is expected if the endpoint doesn't exist
            elif response.status_code == 401:
                print("❌ Protected endpoint returned unauthorized (token may be invalid)")
                return False
            else:
                print(f"⚠️  Protected endpoint returned status: {response.status_code}")
                print(f"   Response: {response.text}")
                return True  # Not necessarily a failure
        except Exception as e:
            print(f"❌ Protected endpoint test failed with error: {e}")
            return False

    def test_refresh_token(self) -> bool:
        """Test the refresh token endpoint."""
        print(f"\n🔍 Testing refresh token endpoint...")
        try:
            response = requests.post(f"{self.base_url}/api/v1/auth/refresh")

            if response.status_code == 200:
                print("✅ Refresh token endpoint working!")
                token_data = response.json()
                print(f"   Token type: {token_data['token_type']}")
                print(f"   New access token: {token_data['access_token'][:20]}...")
                return True
            else:
                print(f"❌ Refresh token failed with status: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Refresh token test failed with error: {e}")
            return False

    def run_all_tests(self):
        """Run all tests in sequence."""
        print("🚀 Starting API endpoint tests...\n")

        # Test basic endpoints
        health_ok = self.test_health_check()
        root_ok = self.test_root_endpoint()

        if not (health_ok and root_ok):
            print("\n❌ Basic endpoints failed. Check if the server is running.")
            return

        # Test authentication flow
        test_email = "<EMAIL>"
        test_name = "Test User"
        test_password = "securepassword123"

        # Register user
        register_ok = self.register_user(test_email, test_name, test_password)

        # Login user
        login_ok = self.login_user(test_email, test_password)

        # Test protected endpoint
        if login_ok:
            protected_ok = self.test_protected_endpoint()

        # Test refresh token
        refresh_ok = self.test_refresh_token()

        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        print(f"Health Check: {'✅ PASS' if health_ok else '❌ FAIL'}")
        print(f"Root Endpoint: {'✅ PASS' if root_ok else '❌ FAIL'}")
        print(f"User Registration: {'✅ PASS' if register_ok else '❌ FAIL'}")
        print(f"User Login: {'✅ PASS' if login_ok else '❌ FAIL'}")
        print(
            f"Protected Endpoint: {'✅ PASS' if 'protected_ok' in locals() and protected_ok else '❌ FAIL'}"
        )
        print(f"Refresh Token: {'✅ PASS' if refresh_ok else '❌ FAIL'}")
        print("=" * 50)


def main():
    """Main function to run the API tests."""
    import sys

    # Check if custom base URL is provided
    base_url = "http://localhost:8000"
    if len(sys.argv) > 1:
        base_url = sys.argv[1]

    print(f"🔗 Testing API at: {base_url}")

    tester = APITester(base_url)
    tester.run_all_tests()


if __name__ == "__main__":
    main()
