# Copilot Instructions for Sainpse Backend

<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

## Project Context
This is a FastAPI backend application for the Sainpse project. The codebase follows modern Python and FastAPI best practices.

## Code Generation Guidelines

### Architecture Patterns
- Follow the repository pattern for data access
- Use dependency injection for services and database connections
- Implement proper error handling with custom exceptions
- Use Pydantic models for request/response validation
- Follow RESTful API design principles

### Code Style
- Use type hints for all function parameters and return values
- Follow PEP 8 style guidelines
- Use descriptive variable and function names
- Add docstrings to all public functions and classes
- Prefer async/await for I/O operations

### Database Operations
- Use SQLAlchemy 2.0 syntax with async sessions
- Implement proper transaction handling
- Use Alembic for database migrations
- Add database indexes for performance-critical queries

### Security
- Always validate and sanitize user inputs
- Use JWT tokens for authentication
- Implement proper CORS policies
- Hash passwords using bcrypt
- Use environment variables for sensitive configuration

### Testing
- Write unit tests for all business logic
- Use pytest fixtures for test setup
- Mock external dependencies in tests
- Aim for high test coverage

### API Design
- Use proper HTTP status codes
- Implement consistent error response formats
- Add request/response examples in docstrings
- Use FastAPI's automatic documentation features
- Implement proper pagination for list endpoints

### Performance
- Use database connection pooling
- Implement caching where appropriate
- Use background tasks for long-running operations
- Optimize database queries to avoid N+1 problems

## Dependencies
- FastAPI for the web framework
- SQLAlchemy for ORM
- Alembic for database migrations
- Pydantic for data validation
- Uvicorn for ASGI server
- pytest for testing
- Redis for caching
