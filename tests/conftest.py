"""
Test configuration and fixtures.

This module provides common test fixtures and configuration for the test suite.
"""

import asyncio
import pytest
from typing import As<PERSON><PERSON>enerator, Generator

from fastapi.testclient import TestClient
from httpx import AsyncClient
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
import mongomock_motor

from app.main import app
from app.models.user import User

# Test database configuration
TEST_DATABASE_NAME = "test_sainpse_db"


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def test_db() -> AsyncGenerator[AsyncIOMotorClient, None]:
    """Create a test database connection using mongomock."""
    # Use mongomock for testing
    client = mongomock_motor.AsyncMongoMockClient()
    database = client[TEST_DATABASE_NAME]

    # Initialize Beanie with test database
    await init_beanie(database=database, document_models=[User])

    yield database

    # Cleanup
    await client.drop_database(TEST_DATABASE_NAME)


@pytest.fixture
def client(test_db) -> TestClient:
    """Create a test client."""
    client = TestClient(app)
    yield client


@pytest.fixture
async def async_client(test_db) -> AsyncGenerator[AsyncClient, None]:
    """Create an async test client."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
