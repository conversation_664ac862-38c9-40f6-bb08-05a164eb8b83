"""
Test enhanced user functionality.

This module tests the enhanced User schema with membership management,
role-based access control, and nested models.
"""

import pytest
from datetime import datetime, timezone
from fastapi.testclient import TestClient

from app.models.user import User, MembershipDetails, ContributionMetrics, MembershipBenefits
from app.core.security import create_access_token, hash_password
from app.core.validation import MembershipValidator, RoleValidator, ValidationError


class TestEnhancedUserRegistration:
    """Test enhanced user registration with new schema."""

    def test_register_user_with_enhanced_fields(self, client: TestClient):
        """Test registration with enhanced user fields."""
        user_data = {
            "email": "<EMAIL>",
            "full_name": "Enhanced User",
            "preferred_name": "Enhanced",
            "password": "securepassword123",
            "bio": "A test user with enhanced features",
            "tags": ["developer", "tester"],
            "affiliations": ["Test Org"],
        }

        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201

        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["full_name"] == user_data["full_name"]
        assert data["preferred_name"] == user_data["preferred_name"]
        assert data["bio"] == user_data["bio"]
        assert data["tags"] == user_data["tags"]
        assert data["affiliations"] == user_data["affiliations"]
        assert data["roles"] == ["user"]  # Default role
        assert data["membership_tier"] == "associate"  # Default tier
        assert data["is_superuser"] is False
        assert "membership_details" in data
        assert "tier_history" in data

    def test_register_user_minimal_fields(self, client: TestClient):
        """Test registration with minimal required fields."""
        user_data = {"email": "<EMAIL>", "password": "securepassword123"}

        response = client.post("/api/v1/auth/register", json=user_data)
        assert response.status_code == 201

        data = response.json()
        assert data["email"] == user_data["email"]
        assert data["full_name"] is None
        assert data["preferred_name"] is None
        assert data["bio"] is None
        assert data["tags"] == []
        assert data["affiliations"] == []
        assert data["roles"] == ["user"]
        assert data["membership_tier"] == "associate"


class TestEnhancedUserProfile:
    """Test enhanced user profile endpoints."""

    @pytest.fixture
    async def enhanced_user(self, test_db) -> User:
        """Create an enhanced test user."""
        membership_details = MembershipDetails(
            joined_at=datetime.now(timezone.utc),
            contributions=ContributionMetrics(
                projects_led=2, projects_participated=5, mentorships=1
            ),
            benefits=MembershipBenefits(
                tool_access_level="beta",
                grant_eligibility=True,
                visibility_boost=True,
                governance_rights="voting",
            ),
        )

        user = User(
            email="<EMAIL>",
            hashed_password=hash_password("securepassword123"),
            full_name="Enhanced User",
            preferred_name="Enhanced",
            bio="Test user with enhanced features",
            roles=["user", "moderator"],
            membership_tier="partner",
            membership_details=membership_details,
            tags=["developer", "moderator"],
            affiliations=["Test Org", "Dev Team"],
        )
        await user.create()
        return user

    def test_get_enhanced_user_profile(self, client: TestClient, enhanced_user: User):
        """Test getting enhanced user profile."""
        # Create access token
        access_token = create_access_token(data={"sub": str(enhanced_user.id)})
        headers = {"Authorization": f"Bearer {access_token}"}

        response = client.get("/api/v1/users/me", headers=headers)
        assert response.status_code == 200

        data = response.json()
        assert data["email"] == enhanced_user.email
        assert data["full_name"] == enhanced_user.full_name
        assert data["preferred_name"] == enhanced_user.preferred_name
        assert data["bio"] == enhanced_user.bio
        assert data["roles"] == enhanced_user.roles
        assert data["membership_tier"] == enhanced_user.membership_tier
        assert data["tags"] == enhanced_user.tags
        assert data["affiliations"] == enhanced_user.affiliations
        assert "membership_details" in data
        assert "contributions" in data["membership_details"]
        assert "benefits" in data["membership_details"]

    def test_update_enhanced_user_profile(self, client: TestClient, enhanced_user: User):
        """Test updating enhanced user profile."""
        access_token = create_access_token(data={"sub": str(enhanced_user.id)})
        headers = {"Authorization": f"Bearer {access_token}"}

        update_data = {
            "preferred_name": "Updated Name",
            "bio": "Updated bio",
            "tags": ["updated", "tags"],
            "affiliations": ["Updated Org"],
        }

        response = client.put("/api/v1/users/me", json=update_data, headers=headers)
        assert response.status_code == 200

        data = response.json()
        assert data["preferred_name"] == update_data["preferred_name"]
        assert data["bio"] == update_data["bio"]
        assert data["tags"] == update_data["tags"]
        assert data["affiliations"] == update_data["affiliations"]


class TestMembershipValidation:
    """Test membership validation logic."""

    def test_valid_tier_transition(self):
        """Test valid tier transitions."""
        validator = MembershipValidator()

        # Associate to Partner (with sufficient contributions)
        contributions = {"projects_led": 1, "projects_participated": 3, "mentorships": 0}
        assert validator.validate_tier_transition("associate", "partner", contributions)

        # Partner to Steward (with sufficient contributions)
        contributions = {"projects_led": 3, "projects_participated": 5, "mentorships": 2}
        assert validator.validate_tier_transition("partner", "steward", contributions)

    def test_invalid_tier_transition(self):
        """Test invalid tier transitions."""
        validator = MembershipValidator()

        # Associate to Steward (skipping partner)
        with pytest.raises(ValidationError):
            validator.validate_tier_transition("associate", "steward")

        # Insufficient contributions
        contributions = {"projects_led": 0, "projects_participated": 1, "mentorships": 0}
        with pytest.raises(ValidationError):
            validator.validate_tier_transition("associate", "partner", contributions)

    def test_forced_tier_transition(self):
        """Test forced tier transitions (admin override)."""
        validator = MembershipValidator()

        # Should work with force=True even with invalid transition
        assert validator.validate_tier_transition("associate", "steward", force=True)

    def test_contribution_metrics_validation(self):
        """Test contribution metrics validation."""
        validator = MembershipValidator()

        # Valid metrics
        assert validator.validate_contribution_metrics(
            projects_led=2, projects_participated=5, mentorships=1
        )

        # Invalid metrics (negative values)
        with pytest.raises(ValidationError):
            validator.validate_contribution_metrics(projects_led=-1)

        # Invalid logic (led > participated)
        with pytest.raises(ValidationError):
            validator.validate_contribution_metrics(projects_led=5, projects_participated=2)


class TestRoleValidation:
    """Test role validation logic."""

    def test_valid_roles(self):
        """Test valid role assignments."""
        validator = RoleValidator()

        # Basic user role
        assert validator.validate_roles(["user"])

        # User with admin role
        assert validator.validate_roles(["user", "admin"])

        # User with multiple roles
        assert validator.validate_roles(["user", "moderator", "staff"])

    def test_invalid_roles(self):
        """Test invalid role assignments."""
        validator = RoleValidator()

        # Empty roles
        with pytest.raises(ValidationError):
            validator.validate_roles([])

        # Invalid role
        with pytest.raises(ValidationError):
            validator.validate_roles(["invalid_role"])

        # Missing dependencies (admin without user)
        with pytest.raises(ValidationError):
            validator.validate_roles(["admin"])

        # Duplicate roles
        with pytest.raises(ValidationError):
            validator.validate_roles(["user", "user"])

    def test_role_assignment_permissions(self):
        """Test role assignment permissions."""
        validator = RoleValidator()

        # Admin can assign most roles
        assert validator.can_assign_roles(["user", "admin"], ["user", "moderator"])

        # Non-admin cannot assign roles
        with pytest.raises(ValidationError):
            validator.can_assign_roles(["user"], ["user", "moderator"])

        # Only superuser can assign admin role
        with pytest.raises(ValidationError):
            validator.can_assign_roles(["user", "admin"], ["user", "admin"])

        # Superuser can assign any role
        assert validator.can_assign_roles(["user", "admin"], ["user", "admin"], is_superuser=True)


class TestTierBenefits:
    """Test tier benefits calculation."""

    def test_associate_benefits(self):
        """Test associate tier benefits."""
        validator = MembershipValidator()
        benefits = validator.get_tier_benefits("associate")

        assert benefits["tool_access_level"] == "general"
        assert benefits["grant_eligibility"] is False
        assert benefits["visibility_boost"] is False
        assert benefits["governance_rights"] == "none"

    def test_partner_benefits(self):
        """Test partner tier benefits."""
        validator = MembershipValidator()
        benefits = validator.get_tier_benefits("partner")

        assert benefits["tool_access_level"] == "beta"
        assert benefits["grant_eligibility"] is True
        assert benefits["visibility_boost"] is True
        assert benefits["governance_rights"] == "voting"

    def test_steward_benefits(self):
        """Test steward tier benefits."""
        validator = MembershipValidator()
        benefits = validator.get_tier_benefits("steward")

        assert benefits["tool_access_level"] == "co-dev"
        assert benefits["grant_eligibility"] is True
        assert benefits["visibility_boost"] is True
        assert benefits["governance_rights"] == "council"


class TestMembershipEndpoints:
    """Test membership management endpoints."""

    @pytest.fixture
    async def admin_user(self, test_db) -> User:
        """Create an admin user for testing."""
        membership_details = MembershipDetails(
            joined_at=datetime.now(timezone.utc),
            contributions=ContributionMetrics(),
            benefits=MembershipBenefits(),
        )

        user = User(
            email="<EMAIL>",
            hashed_password=hash_password("adminpassword123"),
            full_name="Admin User",
            roles=["user", "admin"],
            membership_tier="steward",
            membership_details=membership_details,
        )
        await user.create()
        return user

    @pytest.fixture
    async def regular_user(self, test_db) -> User:
        """Create a regular user for testing."""
        membership_details = MembershipDetails(
            joined_at=datetime.now(timezone.utc),
            contributions=ContributionMetrics(projects_participated=2),
            benefits=MembershipBenefits(),
        )

        user = User(
            email="<EMAIL>",
            hashed_password=hash_password("userpassword123"),
            full_name="Regular User",
            roles=["user"],
            membership_tier="associate",
            membership_details=membership_details,
        )
        await user.create()
        return user

    def test_update_membership_tier_as_admin(
        self, client: TestClient, admin_user: User, regular_user: User
    ):
        """Test updating user membership tier as admin."""
        admin_token = create_access_token(data={"sub": str(admin_user.id)})
        headers = {"Authorization": f"Bearer {admin_token}"}

        tier_update = {
            "tier": "partner",
            "reason": "Demonstrated leadership skills",
            "tier_notes": "Promoted after successful project completion",
        }

        response = client.put(
            f"/api/v1/membership/{regular_user.id}/tier", json=tier_update, headers=headers
        )
        assert response.status_code == 200

        data = response.json()
        assert data["membership_tier"] == "partner"
        assert len(data["tier_history"]) == 1
        assert data["tier_history"][0]["tier"] == "partner"
        assert data["tier_history"][0]["reason"] == tier_update["reason"]

    def test_update_membership_tier_unauthorized(self, client: TestClient, regular_user: User):
        """Test updating membership tier without admin privileges."""
        user_token = create_access_token(data={"sub": str(regular_user.id)})
        headers = {"Authorization": f"Bearer {user_token}"}

        tier_update = {"tier": "partner", "reason": "Self promotion"}

        response = client.put(
            f"/api/v1/membership/{regular_user.id}/tier", json=tier_update, headers=headers
        )
        assert response.status_code == 403

    def test_update_contributions(self, client: TestClient, admin_user: User, regular_user: User):
        """Test updating user contribution metrics."""
        # Partner tier user can update contributions
        partner_user = regular_user
        partner_user.membership_tier = "partner"

        partner_token = create_access_token(data={"sub": str(partner_user.id)})
        headers = {"Authorization": f"Bearer {partner_token}"}

        contributions_update = {"projects_led": 1, "projects_participated": 4, "mentorships": 1}

        response = client.put(
            f"/api/v1/membership/{regular_user.id}/contributions",
            json=contributions_update,
            headers=headers,
        )
        # This might fail due to tier requirements, but tests the endpoint structure
        # In a real scenario, we'd need to properly set up the user's tier first

    def test_get_tier_history(self, client: TestClient, admin_user: User, regular_user: User):
        """Test getting user tier history."""
        user_token = create_access_token(data={"sub": str(regular_user.id)})
        headers = {"Authorization": f"Bearer {user_token}"}

        response = client.get(f"/api/v1/membership/{regular_user.id}/tier-history", headers=headers)
        assert response.status_code == 200

        data = response.json()
        assert isinstance(data, list)  # Should return list of tier history entries
