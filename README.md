# Sainpse Backend

A modern FastAPI backend application for the Sainpse project.

## Features

- 🚀 FastAPI framework with automatic API documentation
- 🔒 JWT authentication and authorization
- 🗄️ SQLAlchemy ORM with PostgreSQL support
- 📊 Database migrations with Alembic
- ⚡ Redis for caching and session management
- 🧪 Comprehensive testing with pytest
- 🐳 Docker support for containerization
- 📝 Automatic API documentation with Swagger UI

## Project Structure

```
sainpse-backend/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application entry point
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py        # Application configuration
│   │   ├── security.py      # Authentication and security
│   │   └── database.py      # Database configuration
│   ├── api/
│   │   ├── __init__.py
│   │   ├── deps.py          # API dependencies
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── api.py       # API router
│   │       └── endpoints/   # API endpoints
│   ├── models/              # SQLAlchemy models
│   ├── schemas/             # Pydantic schemas
│   ├── crud/                # CRUD operations
│   └── utils/               # Utility functions
├── tests/                   # Test files
├── alembic/                 # Database migrations
├── docker-compose.yml       # Docker services
├── Dockerfile               # Application container
├── requirements.txt         # Python dependencies
└── .env.example            # Environment variables template
```

## Quick Start

### 1. Set up the environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Set up environment variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your configuration
```

### 3. Run the application

```bash
# Start the development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access the API

- **API Documentation (Swagger UI)**: http://localhost:8000/docs
- **Alternative Documentation (ReDoc)**: http://localhost:8000/redoc
- **API Base URL**: http://localhost:8000/api/v1

## Development

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app tests/
```

### Database Migrations

```bash
# Create a new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head
```

### Docker Development

```bash
# Build and run with Docker Compose
docker-compose up --build

# Run in background
docker-compose up -d
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh access token

### Users
- `GET /api/v1/users/me` - Get current user profile
- `PUT /api/v1/users/me` - Update current user profile

More endpoints will be added as the project grows.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run the test suite
6. Submit a pull request

## License

This project is licensed under the MIT License.
